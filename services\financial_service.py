"""
Financial Model Service
=======================

Service for financial modeling calculations and analysis.
"""

from typing import Dict, Any, Optional, Callable
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Use local enhanced DCF model for 2025 standards
from .enhanced_dcf_model import EnhancedDCFModel, EnhancedDCFAssumptions
from .error_handler import error_handler, ErrorSeverity, FallbackManager, FinancialModelError
from models.project_assumptions import EnhancedProjectAssumptions


class FinancialModelService:
    """Enhanced Financial Model Service with 2025 DCF standards."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.dcf_model = EnhancedDCFModel()
        self._current_results: Optional[Dict[str, Any]] = None
        self._sensitivity_results: Optional[pd.DataFrame] = None
        self._monte_carlo_results: Optional[Dict[str, Any]] = None
        self._scenario_results: Optional[Dict[str, Any]] = None
    
    @error_handler(severity=ErrorSeverity.HIGH,
                  user_message="Financial model calculation failed. Please check your inputs and try again.",
                  fallback_return=None)
    def run_financial_model(self,
                          assumptions: EnhancedProjectAssumptions,
                          progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run the enhanced DCF financial model with 2025 standards."""
        try:
            if progress_callback:
                progress_callback(10, "Validating assumptions...")

            # Validate assumptions
            validation_errors = assumptions.validate_all()
            if validation_errors:
                raise ValueError(f"Validation errors: {validation_errors}")

            if progress_callback:
                progress_callback(30, "Building enhanced DCF cashflow model...")

            # Convert to DCF assumptions format
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            # Build enhanced cashflow with DCF model
            cashflow = self.dcf_model.build_cashflow(dcf_assumptions)

            if progress_callback:
                progress_callback(60, "Computing enhanced KPIs and DCF metrics...")

            # Compute comprehensive KPIs
            kpis = self.dcf_model.compute_kpis(cashflow, dcf_assumptions)

            if progress_callback:
                progress_callback(90, "Finalizing results with DCF analysis...")

            # Store enhanced results
            self._current_results = {
                'cashflow': cashflow,
                'kpis': kpis,
                'assumptions': assumptions.to_dict(),
                'dcf_assumptions': dcf_assumptions.__dict__,
                'execution_time': datetime.now().isoformat(),
                'model_version': '3.0.0_DCF_Enhanced'
            }

            if progress_callback:
                progress_callback(100, "Enhanced DCF model completed successfully")

            self.logger.info("Enhanced DCF financial model executed successfully")
            return self._current_results

        except Exception as e:
            self.logger.error(f"Error running enhanced DCF financial model: {str(e)}")
            # Return fallback results to maintain application stability
            fallback_results = FallbackManager.get_fallback_financial_results()
            fallback_results['error_message'] = str(e)
            return fallback_results
    
    @error_handler(severity=ErrorSeverity.MEDIUM,
                  user_message="Sensitivity analysis failed. Using default parameters.",
                  fallback_return=None)
    def run_sensitivity_analysis(self,
                               assumptions: EnhancedProjectAssumptions,
                               variables: Optional[list] = None,
                               progress_callback: Optional[Callable[[float, str], None]] = None) -> pd.DataFrame:
        """Run sensitivity analysis."""
        try:
            if variables is None:
                variables = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate']
            
            if progress_callback:
                progress_callback(20, "Setting up sensitivity analysis...")
            
            # Convert to DCF assumptions for enhanced sensitivity analysis
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if progress_callback:
                progress_callback(50, "Running enhanced sensitivity calculations...")

            # Use enhanced sensitivity analysis with DCF model
            self._sensitivity_results = self._run_enhanced_sensitivity_analysis(dcf_assumptions, variables)
            
            if progress_callback:
                progress_callback(100, "Sensitivity analysis completed")
            
            self.logger.info("Sensitivity analysis completed successfully")
            return self._sensitivity_results
            
        except Exception as e:
            self.logger.error(f"Error running sensitivity analysis: {str(e)}")
            # Return fallback sensitivity results
            return FallbackManager.get_fallback_sensitivity_results()
    
    def run_monte_carlo_simulation(self,
                                 assumptions: EnhancedProjectAssumptions,
                                 n_simulations: int = 1000,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run Monte Carlo simulation."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up Monte Carlo simulation...")
            
            # Convert to DCF assumptions for enhanced Monte Carlo
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if progress_callback:
                progress_callback(30, f"Running {n_simulations} enhanced simulations...")

            # Run enhanced Monte Carlo simulation with DCF model
            mc_results = self._run_enhanced_monte_carlo(dcf_assumptions, n_simulations, progress_callback)
            
            if progress_callback:
                progress_callback(80, "Generating statistics...")
            
            # Generate statistics
            mc_stats = generate_monte_carlo_statistics(mc_results)
            
            self._monte_carlo_results = {
                'results': mc_results,
                'statistics': mc_stats,
                'n_simulations': n_simulations
            }
            
            if progress_callback:
                progress_callback(100, "Monte Carlo simulation completed")
            
            self.logger.info(f"Monte Carlo simulation completed with {n_simulations} simulations")
            return self._monte_carlo_results
            
        except Exception as e:
            self.logger.error(f"Error running Monte Carlo simulation: {str(e)}")
            raise
    
    def run_scenario_analysis(self,
                            assumptions: EnhancedProjectAssumptions,
                            progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run scenario analysis."""
        try:
            if progress_callback:
                progress_callback(20, "Setting up scenarios...")
            
            # Run enhanced scenarios
            self._scenario_results = run_enhanced_scenarios()
            
            if progress_callback:
                progress_callback(100, "Scenario analysis completed")
            
            self.logger.info("Scenario analysis completed successfully")
            return self._scenario_results
            
        except Exception as e:
            self.logger.error(f"Error running scenario analysis: {str(e)}")
            raise
    
    def _convert_to_dcf_assumptions(self, assumptions: EnhancedProjectAssumptions) -> EnhancedDCFAssumptions:
        """Convert UI assumptions to enhanced DCF assumptions format."""
        return EnhancedDCFAssumptions(
            capacity_mw=assumptions.capacity_mw,
            production_mwh_year1=assumptions.production_mwh_year1,
            project_life_years=assumptions.project_life_years,
            capex_meur=assumptions.capex_meur,
            opex_keuros_year1=assumptions.opex_keuros_year1,
            ppa_price_eur_kwh=assumptions.ppa_price_eur_kwh,
            ppa_escalation=assumptions.ppa_escalation,
            debt_ratio=assumptions.debt_ratio,
            interest_rate=assumptions.interest_rate,
            debt_years=assumptions.debt_years,
            grace_years=getattr(assumptions, 'grace_years', 2),
            discount_rate=assumptions.discount_rate,
            tax_rate=assumptions.tax_rate,
            tax_holiday=getattr(assumptions, 'tax_holiday', 0),
            degradation_year1=getattr(assumptions, 'degradation_year1', 0.025),
            degradation_annual=getattr(assumptions, 'degradation_rate', 0.005),
            land_lease_eur_mw_year=assumptions.land_lease_eur_mw_year,
            total_grants_meur=assumptions.calculate_total_grants(),
            # Enhanced 2025 parameters with defaults
            terminal_growth_rate=0.025,
            terminal_value_method="perpetuity",
            use_terminal_value=True,
            working_capital_days=30,
            insurance_rate=0.002,
            opex_escalation=0.02
        )

    def _run_enhanced_sensitivity_analysis(self, dcf_assumptions, variables):
        """Run enhanced sensitivity analysis with DCF model."""
        try:
            import numpy as np
            import pandas as pd

            # Define sensitivity ranges (±20% for each variable)
            sensitivity_ranges = {
                'production_mwh_year1': [-0.2, -0.1, 0, 0.1, 0.2],
                'ppa_price_eur_kwh': [-0.2, -0.1, 0, 0.1, 0.2],
                'capex_meur': [-0.2, -0.1, 0, 0.1, 0.2],
                'discount_rate': [-0.02, -0.01, 0, 0.01, 0.02],  # Absolute changes for rates
                'opex_keuros_year1': [-0.2, -0.1, 0, 0.1, 0.2]
            }

            results = []
            base_assumptions = dcf_assumptions

            # Calculate base case
            base_cashflow = self.dcf_model.build_cashflow(base_assumptions)
            base_kpis = self.dcf_model.compute_kpis(base_cashflow, base_assumptions)
            base_irr = base_kpis.get('IRR_equity', 0)

            for variable in variables:
                if variable not in sensitivity_ranges:
                    continue

                for change in sensitivity_ranges[variable]:
                    # Create modified assumptions
                    modified_assumptions = dcf_assumptions.__class__(**dcf_assumptions.__dict__)

                    current_value = getattr(modified_assumptions, variable)
                    if variable == 'discount_rate':
                        # Absolute change for discount rate
                        new_value = current_value + change
                    else:
                        # Percentage change for other variables
                        new_value = current_value * (1 + change)

                    setattr(modified_assumptions, variable, new_value)

                    # Run model with modified assumptions
                    try:
                        modified_cashflow = self.dcf_model.build_cashflow(modified_assumptions)
                        modified_kpis = self.dcf_model.compute_kpis(modified_cashflow, modified_assumptions)
                        modified_irr = modified_kpis.get('IRR_equity', 0)

                        results.append({
                            'Variable': variable,
                            'Change_%': change * 100,
                            'IRR_equity': modified_irr,
                            'IRR_change_%': (modified_irr - base_irr) * 100,
                            'NPV_equity': modified_kpis.get('NPV_equity', 0),
                            'LCOE': modified_kpis.get('LCOE_eur_kwh', 0)
                        })
                    except Exception as e:
                        self.logger.warning(f"Sensitivity calculation failed for {variable} at {change}: {e}")
                        continue

            return pd.DataFrame(results)

        except Exception as e:
            self.logger.error(f"Enhanced sensitivity analysis failed: {e}")
            # Return empty DataFrame as fallback
            return pd.DataFrame()

    def _run_enhanced_monte_carlo(self, dcf_assumptions, n_simulations, progress_callback=None):
        """Run enhanced Monte Carlo simulation with DCF model."""
        try:
            import numpy as np

            results = {
                'IRR_equity': [],
                'IRR_project': [],
                'NPV_equity': [],
                'NPV_project': [],
                'LCOE_eur_kwh': []
            }

            # Define uncertainty distributions
            uncertainty_params = {
                'production_mwh_year1': {'type': 'normal', 'std': 0.1},  # 10% std dev
                'ppa_price_eur_kwh': {'type': 'normal', 'std': 0.05},   # 5% std dev
                'capex_meur': {'type': 'normal', 'std': 0.15},          # 15% std dev
                'opex_keuros_year1': {'type': 'normal', 'std': 0.1},    # 10% std dev
                'discount_rate': {'type': 'normal', 'std': 0.01}        # 1% absolute std dev
            }

            for i in range(n_simulations):
                if progress_callback and i % max(1, n_simulations // 10) == 0:
                    progress = 30 + (i / n_simulations) * 60  # 30% to 90%
                    progress_callback(progress, f"Simulation {i+1}/{n_simulations}")

                # Create random assumptions
                random_assumptions = dcf_assumptions.__class__(**dcf_assumptions.__dict__)

                for param, config in uncertainty_params.items():
                    base_value = getattr(random_assumptions, param)

                    if config['type'] == 'normal':
                        if param == 'discount_rate':
                            # Absolute variation for discount rate
                            random_value = np.random.normal(base_value, config['std'])
                        else:
                            # Relative variation for other parameters
                            random_value = np.random.normal(base_value, base_value * config['std'])

                        # Ensure positive values
                        random_value = max(0.001, random_value)
                        setattr(random_assumptions, param, random_value)

                try:
                    # Run DCF model with random assumptions
                    cashflow = self.dcf_model.build_cashflow(random_assumptions)
                    kpis = self.dcf_model.compute_kpis(cashflow, random_assumptions)

                    # Store results
                    for key in results.keys():
                        results[key].append(kpis.get(key, 0))

                except Exception as e:
                    self.logger.warning(f"Monte Carlo iteration {i} failed: {e}")
                    # Add NaN values for failed iterations
                    for key in results.keys():
                        results[key].append(np.nan)

            # Generate statistics
            statistics = {}
            for key, values in results.items():
                clean_values = [v for v in values if not np.isnan(v)]
                if clean_values:
                    statistics[key] = {
                        'mean': np.mean(clean_values),
                        'std': np.std(clean_values),
                        'min': np.min(clean_values),
                        'max': np.max(clean_values),
                        'p5': np.percentile(clean_values, 5),
                        'p25': np.percentile(clean_values, 25),
                        'p50': np.percentile(clean_values, 50),
                        'p75': np.percentile(clean_values, 75),
                        'p95': np.percentile(clean_values, 95)
                    }
                else:
                    statistics[key] = {k: 0 for k in ['mean', 'std', 'min', 'max', 'p5', 'p25', 'p50', 'p75', 'p95']}

            return {
                'results': results,
                'statistics': statistics,
                'n_simulations': n_simulations,
                'success_rate': len([v for v in results['IRR_equity'] if not np.isnan(v)]) / n_simulations
            }

        except Exception as e:
            self.logger.error(f"Enhanced Monte Carlo simulation failed: {e}")
            # Return empty results as fallback
            return {
                'results': {key: [] for key in ['IRR_equity', 'IRR_project', 'NPV_equity', 'NPV_project', 'LCOE_eur_kwh']},
                'statistics': {},
                'n_simulations': 0,
                'success_rate': 0
            }
    
    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """Get current financial model results."""
        return self._current_results
    
    def get_sensitivity_results(self) -> Optional[pd.DataFrame]:
        """Get sensitivity analysis results."""
        return self._sensitivity_results
    
    def get_monte_carlo_results(self) -> Optional[Dict[str, Any]]:
        """Get Monte Carlo simulation results."""
        return self._monte_carlo_results
    
    def get_scenario_results(self) -> Optional[Dict[str, Any]]:
        """Get scenario analysis results."""
        return self._scenario_results
    
    def has_results(self) -> bool:
        """Check if financial model has been run."""
        return self._current_results is not None
    
    def clear_results(self):
        """Clear all cached results."""
        self._current_results = None
        self._sensitivity_results = None
        self._monte_carlo_results = None
        self._scenario_results = None
        self.logger.info("All results cleared")
    
    def get_kpi_summary(self) -> Optional[Dict[str, Any]]:
        """Get KPI summary for display."""
        if not self._current_results:
            return None
        
        kpis = self._current_results.get('kpis', {})
        return {
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'npv_project': kpis.get('NPV_project', 0),
            'npv_equity': kpis.get('NPV_equity', 0),
            'lcoe': kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'avg_dscr': kpis.get('Avg_DSCR', 0),
            'payback_years': kpis.get('Payback_years', 0),
            'grant_percentage': kpis.get('Grant_percentage', 0)
        }
    
    def get_cashflow_summary(self) -> Optional[Dict[str, Any]]:
        """Get cashflow summary for display."""
        if not self._current_results:
            return None
        
        cashflow = self._current_results.get('cashflow')
        if cashflow is None:
            return None
        
        if isinstance(cashflow, dict):
            df = pd.DataFrame(cashflow)
        else:
            df = cashflow
        
        return {
            'total_revenue': df['Revenue'].sum(),
            'total_opex': abs(df['OPEX'].sum()),
            'total_capex': abs(df['Capex'].sum()),
            'total_grants': df['Grants'].sum(),
            'project_life': len(df) - 1,  # Excluding year 0
            'first_year_cf': df.loc[1, 'Equity_CF'] if 1 in df.index else 0,
            'last_year_cf': df.iloc[-1]['Equity_CF'] if len(df) > 0 else 0
        }
